import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm'

export class AddMicrosoftOAuthFields1703000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add microsoftId column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'microsoftId',
        type: 'varchar',
        length: '255',
        isNullable: true
      })
    )

    // Add displayName column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'displayName',
        type: 'varchar',
        length: '255',
        isNullable: true
      })
    )

    // Create index on microsoftId for faster lookups
    await queryRunner.query(`CREATE INDEX "IDX_USER_MICROSOFT_ID" ON "users" ("microsoftId")`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop index
    await queryRunner.query(`DROP INDEX "IDX_USER_MICROSOFT_ID"`)

    // Drop columns
    await queryRunner.dropColumn('users', 'displayName')
    await queryRunner.dropColumn('users', 'microsoftId')
  }
}
