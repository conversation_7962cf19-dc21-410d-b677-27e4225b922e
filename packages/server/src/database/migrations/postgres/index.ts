import { Init1693891895163 } from './1693891895163-Init'
import { UserGroup1737531084639 } from './1737531084639-UserGroup'
import { UserGroup1738567879827 } from './1738567879827-UserGroup'
import { RaladionCasadeChatflow1738913521499 } from './1738913521499-raladionCasadeChatflow'
import { AddChatFlowGroupName1739258457660 } from './1739258457660-add-chatflow-groupname'
import { AddFieldIsPublish1739335682895 } from './1739335682895-addFieldIsPublish'
import { AddIsUseFAQ1742977020876 } from './1742977020876-addIsUseFAQ'
import { AddTableQA1744260983643 } from './1744260983643-addTableQA'
import { UpdateTableQA1744270293224 } from './1744270293224-UpdateTableQA'
import { UpdateTableQAAddUserId1744359669284 } from './1744359669284-UpdateTableQAAddUserId'
import { UpdateTableQATableAddUserId1744367625387 } from './1744367625387-UpdateTableQATableAddUserId'
import { UpdateFeebbackMessage1744598612079 } from './1744598612079-UpdateFeebbackMessage'
import { AddTableSession1744797380827 } from './1744797380827-addTableSession'
import { UpdateTableUsers1744959321009 } from './1744959321009-updateTableUsers'
import { UpdateTableUsergroup1745490615276 } from './1745490615276-update-table-usergroup'
import { AddTableLabel1745565382242 } from './1745565382242-addTableLabel'
import { AddShowDashboardFlow1745569176138 } from './1745569176138-addShowDashboardFlow'
import { AddTableIngestionJob1745827595478 } from './1745827595478-addTableIngestionJob'
import { UpdateTableIngestionJobsStatus1745897313368 } from './1745897313368-updateTableIngestionJobsStatus'
import { AddTableKnowledgeBase1746515646975 } from './1746515646975-addTableKnowledgeBase'
import { UpdateTableGroupUsersFieldDisplayPrefixes1746516330761 } from './1746516330761-updateTableGroupUsersFieldDisplayPrefixes'
import { UpdateTableChatflowFieldGroupNamesSharedWithUsers1747034375230 } from './1747034375230-updateTableChatflowFieldGroupNamesSharedWithUsers'
import { UpdateFieldScanEd1747626516702 } from './1747626516702-updateFieldScanEd'
import { UpdateFieldScanEdForAQ1747627275937 } from './1747627275937-updateFieldScanEdForAQ'
import { UpdateRalationUser1750127089775 } from './1750127089775-update_ralation_user'
import { AddMicrosoftOAuthFields1750200000000 } from './1750200000000-AddMicrosoftOAuthFields'

export const postgresMigrations = [
  Init1693891895163,
  UserGroup1737531084639,
  UserGroup1738567879827,
  RaladionCasadeChatflow1738913521499,
  AddChatFlowGroupName1739258457660,
  AddFieldIsPublish1739335682895,
  AddIsUseFAQ1742977020876,
  AddTableQA1744260983643,
  UpdateTableQA1744270293224,
  UpdateTableQAAddUserId1744359669284,
  UpdateTableQATableAddUserId1744367625387,
  UpdateFeebbackMessage1744598612079,
  AddTableSession1744797380827,
  UpdateTableUsers1744959321009,
  UpdateTableUsergroup1745490615276,
  AddTableLabel1745565382242,
  AddShowDashboardFlow1745569176138,
  AddTableIngestionJob1745827595478,
  UpdateTableIngestionJobsStatus1745897313368,
  AddTableKnowledgeBase1746515646975,
  UpdateTableGroupUsersFieldDisplayPrefixes1746516330761,
  UpdateTableChatflowFieldGroupNamesSharedWithUsers1747034375230,
  UpdateFieldScanEd1747626516702,
  UpdateFieldScanEdForAQ1747627275937,
  UpdateRalationUser1750127089775,
  AddMicrosoftOAuthFields1750200000000
]
